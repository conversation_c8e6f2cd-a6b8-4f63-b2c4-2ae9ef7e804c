#!/bin/bash
set -e

# This script creates the GCS bucket for Terraform state storage
# It should be run once before the first Terraform apply

# Get variables from environment
PROJECT_ID=${PROJECT_ID:-$(gcloud config get-value project)}
REGION=${REGION:-asia-southeast1}
STATE_BUCKET_NAME="${PROJECT_ID}-terraform-state"

echo "Creating Terraform state bucket: ${STATE_BUCKET_NAME}"

# Check if bucket exists
if gsutil ls -p ${PROJECT_ID} gs://${STATE_BUCKET_NAME} &>/dev/null; then
  echo "Bucket ${STATE_BUCKET_NAME} already exists, skipping creation."
else
  # Create the bucket
  gsutil mb -p ${PROJECT_ID} -l ${REGION} gs://${STATE_BUCKET_NAME}
  
  # Enable versioning
  gsutil versioning set on gs://${STATE_BUCKET_NAME}
  
  # Set lifecycle rules to clean up old versions
  cat > /tmp/lifecycle.json << EOF
{
  "rule": [
    {
      "action": {"type": "Delete"},
      "condition": {
        "numNewerVersions": 10,
        "isLive": false
      }
    }
  ]
}
EOF
  
  gsutil lifecycle set /tmp/lifecycle.json gs://${STATE_BUCKET_NAME}
  rm /tmp/lifecycle.json
  
  echo "Bucket ${STATE_BUCKET_NAME} created successfully with versioning enabled."
fi

# Update the backend.tf file with the correct bucket name
BACKEND_FILE="terraform/environments/prod/backend.tf"

echo "Updating ${BACKEND_FILE} with bucket name: ${STATE_BUCKET_NAME}"

cat > ${BACKEND_FILE} << EOF
terraform {
  backend "gcs" {
    bucket = "${STATE_BUCKET_NAME}"
    prefix = "terraform/state"
  }
}
EOF

echo "Bootstrap complete. Terraform state will be stored in gs://${STATE_BUCKET_NAME}/terraform/state"
