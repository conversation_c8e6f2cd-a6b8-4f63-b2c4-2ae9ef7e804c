steps:
  # Debug step to show the repository contents
  - name: 'ubuntu'
    id: 'debug-repo'
    args: ['ls', '-la']

  # Check server-nest directory after submodule initialization
  - name: 'ubuntu'
    id: 'check-server-nest'
    args: ['ls', '-la', 'server-nest/']

  # Check client directory
  - name: 'ubuntu'
    id: 'check-client'
    args: ['ls', '-la', 'client/']

  # Install dependencies for Terraform
  - name: 'hashicorp/terraform:1.5.0'
    id: 'tf-init'
    entrypoint: 'sh'
    args:
      - '-c'
      - |
        cd terraform
        terraform init

  # Validate Terraform configuration
  - name: 'hashicorp/terraform:1.5.0'
    id: 'tf-validate'
    entrypoint: 'sh'
    args:
      - '-c'
      - |
        cd terraform
        terraform validate

  # Plan Terraform changes (for PR preview)
  - name: 'hashicorp/terraform:1.5.0'
    id: 'tf-plan'
    entrypoint: 'sh'
    args:
      - '-c'
      - |
        cd terraform
        terraform plan -var-file=terraform.tfvars

  # Build server image using Dockerfile.prod
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-server'
    dir: 'server-nest'  # Work in the server-nest directory
    entrypoint: 'sh'
    args:
      - '-c'
      - |
        # Debug: List directory contents before build
        echo "Directory contents before build:"
        ls -la

        # Proceed with the build
        docker build \
          -t ${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/ielts-server:latest \
          -f Dockerfile.prod \
          .

  # Build client image using Dockerfile.prod
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-client'
    dir: 'client'  # Work in the client directory
    entrypoint: 'sh'
    args:
      - '-c'
      - |
        # Debug: List directory contents before build
        echo "Directory contents before build:"
        ls -la

        # Proceed with the build
        docker build \
          -t ${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/ielts-client:latest \
          -f Dockerfile.prod \
          .

  # Push the server image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-server'
    args: ['push', '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/ielts-server:latest']

  # Push the client image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-client'
    args: ['push', '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/ielts-client:latest']

  # Apply Terraform changes (only on main branch)
  - name: 'hashicorp/terraform:1.5.0'
    id: 'tf-apply'
    entrypoint: 'sh'
    args:
      - '-c'
      - |
        if [ "$BRANCH_NAME" = "main" ]; then
          cd terraform
          terraform apply -auto-approve -var-file=terraform.tfvars
        else
          echo "Skipping Terraform apply on non-main branch"
        fi

  # Run database migrations (only on main branch)
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'run-migrations'
    entrypoint: 'sh'
    args:
      - '-c'
      - |
        if [ "$BRANCH_NAME" = "main" ]; then
          gcloud run jobs execute db-migrate --region=${_REGION} --wait
        else
          echo "Skipping database migrations on non-main branch"
        fi

  # Output the URLs of the deployed services
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'output-urls'
    entrypoint: 'sh'
    args:
      - '-c'
      - |
        if [ "$BRANCH_NAME" = "main" ]; then
          echo "Backend URL: $(gcloud run services describe ielts-server --region=${_REGION} --format='value(status.url)')"
          echo "Frontend URL: $(gcloud run services describe ielts-client --region=${_REGION} --format='value(status.url)')"
        else
          echo "Deployment URLs will be available after merging to main"
        fi

substitutions:
  _REGION: asia-southeast1
  _REPOSITORY: ielts-repo

options:
  logging: CLOUD_LOGGING_ONLY
