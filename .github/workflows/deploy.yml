name: Deploy to GCP Cloud Run

on:
  push:
    branches: [ main ]
    paths:
      - 'client/**'
      - 'server-nest/**'
      - 'terraform/**'
      - 'instructions/**'
      - '.github/workflows/deploy.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'client/**'
      - 'server-nest/**'
      - 'terraform/**'
      - '.github/workflows/deploy.yml'
  workflow_dispatch:
    inputs:
      deploy_infra_only:
        description: 'Deploy infrastructure changes only (skip image builds)'
        required: false
        default: 'false'
        type: boolean
      deploy_images_only:
        description: 'Deploy image changes only (skip infrastructure changes)'
        required: false
        default: 'false'
        type: boolean

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  REGION: asia-southeast1
  REPOSITORY: ielts-repo
  TF_LOG: INFO
  TF_VAR_project_id: ${{ secrets.GCP_PROJECT_ID }}
  TF_VAR_region: asia-southeast1

jobs:
  # Common setup job that other jobs will depend on
  setup:
    name: Setup
    runs-on: ubuntu-latest
    outputs:
      access_token: ${{ steps.auth_token.outputs.access_token }}

    steps:
    - name: Checkout
      uses: actions/checkout@v3
      with:
        submodules: recursive
        token: ${{ secrets.PAT_TOKEN }}

    - name: Initialize Git submodules
      run: |
        git submodule init
        git submodule update

    - name: Authenticate to Google Cloud
      id: auth
      run: |
        # Write the service account key to a file
        echo '${{ secrets.GCP_SA_KEY }}' > sa-key.json

        # Authenticate with the service account key
        gcloud auth activate-service-account --key-file=sa-key.json

        # Set the application default credentials
        export GOOGLE_APPLICATION_CREDENTIALS="$(pwd)/sa-key.json"
        echo "GOOGLE_APPLICATION_CREDENTIALS=$(pwd)/sa-key.json" >> $GITHUB_ENV

    - name: Set up Google Cloud SDK
      uses: google-github-actions/setup-gcloud@v1
      with:
        project_id: ${{ env.PROJECT_ID }}

    - name: Configure Docker for Artifact Registry
      run: gcloud auth configure-docker ${{ env.REGION }}-docker.pkg.dev --quiet

    - name: Get GCP access token
      id: auth_token
      run: echo "access_token=$(gcloud auth print-access-token)" >> $GITHUB_OUTPUT

  # Build server image in parallel
  build-server:
    name: Build Server Image
    needs: setup
    if: ${{ github.event.inputs.deploy_infra_only != 'true' }}
    runs-on: ubuntu-latest

    steps:
    - name: Checkout
      uses: actions/checkout@v3
      with:
        submodules: recursive
        token: ${{ secrets.PAT_TOKEN }}

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    - name: Configure Docker for Artifact Registry
      run: gcloud auth configure-docker ${{ env.REGION }}-docker.pkg.dev --quiet

    - name: Authenticate to Google Cloud
      run: |
        # Write the service account key to a file
        echo '${{ secrets.GCP_SA_KEY }}' > sa-key.json

        # Authenticate with the service account key
        gcloud auth activate-service-account --key-file=sa-key.json

        # Set the application default credentials
        export GOOGLE_APPLICATION_CREDENTIALS="$(pwd)/sa-key.json"
        echo "GOOGLE_APPLICATION_CREDENTIALS=$(pwd)/sa-key.json" >> $GITHUB_ENV

    - name: Build and push server image
      uses: docker/build-push-action@v4
      with:
        context: ./server-nest
        file: ./server-nest/Dockerfile.prod
        push: true
        tags: |
          ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/ielts-server:${{ github.sha }}
          ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/ielts-server:latest
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # Build client image in parallel
  build-client:
    name: Build Client Image
    needs: setup
    if: ${{ github.event.inputs.deploy_infra_only != 'true' }}
    runs-on: ubuntu-latest

    steps:
    - name: Checkout
      uses: actions/checkout@v3
      with:
        submodules: recursive
        token: ${{ secrets.PAT_TOKEN }}

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    - name: Configure Docker for Artifact Registry
      run: gcloud auth configure-docker ${{ env.REGION }}-docker.pkg.dev --quiet

    - name: Authenticate to Google Cloud
      run: |
        # Write the service account key to a file
        echo '${{ secrets.GCP_SA_KEY }}' > sa-key.json

        # Authenticate with the service account key
        gcloud auth activate-service-account --key-file=sa-key.json

        # Set the application default credentials
        export GOOGLE_APPLICATION_CREDENTIALS="$(pwd)/sa-key.json"
        echo "GOOGLE_APPLICATION_CREDENTIALS=$(pwd)/sa-key.json" >> $GITHUB_ENV

    - name: Build and push client image
      uses: docker/build-push-action@v4
      with:
        context: ./client
        file: ./client/Dockerfile.prod
        push: true
        tags: |
          ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/ielts-client:${{ github.sha }}
          ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/ielts-client:latest
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # Infrastructure deployment job
  deploy-infra:
    name: Deploy Infrastructure
    needs: [setup, build-server, build-client]
    if: ${{ github.event.inputs.deploy_images_only != 'true' && (github.event.inputs.deploy_infra_only == 'true' || (github.ref == 'refs/heads/main' && github.event_name == 'push')) }}
    runs-on: ubuntu-latest

    steps:
    - name: Checkout
      uses: actions/checkout@v3
      with:
        submodules: recursive
        token: ${{ secrets.PAT_TOKEN }}

    - name: Authenticate to Google Cloud
      run: |
        # Write the service account key to a file
        echo '${{ secrets.GCP_SA_KEY }}' > sa-key.json

        # Authenticate with the service account key
        gcloud auth activate-service-account --key-file=sa-key.json

        # Set the application default credentials
        export GOOGLE_APPLICATION_CREDENTIALS="$(pwd)/sa-key.json"
        echo "GOOGLE_APPLICATION_CREDENTIALS=$(pwd)/sa-key.json" >> $GITHUB_ENV

    - name: Set up Google Cloud SDK
      uses: google-github-actions/setup-gcloud@v1
      with:
        project_id: ${{ env.PROJECT_ID }}

    - name: Set up Terraform
      uses: hashicorp/setup-terraform@v2
      with:
        terraform_version: 1.11.4

    - name: Create terraform.tfvars
      run: |
        cat > terraform/environments/prod/terraform.tfvars << EOF
        # Project and region configuration
        project_id   = "${{ secrets.GCP_PROJECT_ID }}"
        region       = "${{ env.REGION }}"
        zone         = "${{ env.REGION }}-a"

        # API configuration
        apis = [
          "cloudresourcemanager.googleapis.com",
          "compute.googleapis.com",
          "containerregistry.googleapis.com",
          "artifactregistry.googleapis.com",
          "run.googleapis.com",
          "vpcaccess.googleapis.com",
          "sqladmin.googleapis.com",
          "secretmanager.googleapis.com",
          "monitoring.googleapis.com",
          "logging.googleapis.com",
          "cloudbuild.googleapis.com"
        ]

        # Networking configuration
        network_name   = "ielts-toolkit-network"
        subnet_name    = "ielts-toolkit-subnet"
        subnet_cidr    = "10.0.0.0/24"
        connector_name = "ielts-toolkit-connector"
        connector_cidr = "10.8.0.0/28"

        # Database configuration
        db_instance_name = "ielts-toolkit-db"
        db_tier          = "db-f1-micro"
        db_name          = "ielts_toolkit"
        db_user          = "ielts_admin"

        # Registry configuration
        repository_id     = "${{ env.REPOSITORY }}"
        repository_format = "DOCKER"

        # Cloud Run configuration
        backend_name       = "ielts-server"
        frontend_name      = "ielts-client"
        migration_job_name = "db-migrate"
        backend_cpu        = "1000m"
        backend_memory     = "512Mi"
        frontend_cpu       = "1000m"
        frontend_memory    = "512Mi"
        backend_min_scale  = 1
        backend_max_scale  = 10
        frontend_min_scale = 1
        frontend_max_scale = 5

        # Monitoring configuration
        notification_email           = "${{ secrets.NOTIFICATION_EMAIL }}"
        cpu_utilization_threshold    = 80
        memory_utilization_threshold = 80
        error_rate_threshold         = 5
        latency_threshold            = 500
        EOF

    - name: Terraform Init
      id: init
      run: |
        cd terraform/environments/prod

        # Ensure the credentials file is properly set
        echo "Using credentials from $GOOGLE_APPLICATION_CREDENTIALS"

        # Try to initialize with GCS backend
        if ! terraform init; then
          echo "GCS backend initialization failed. The state bucket might not exist yet."
          echo "Running bootstrap script to create the state bucket..."
          cd ../../../
          ./scripts/bootstrap.sh
          cd terraform/environments/prod
          terraform init
        fi

# Removed Terraform Format check to avoid formatting issues

    - name: Terraform Validate
      id: validate
      run: |
        cd terraform/environments/prod
        terraform validate -no-color

    - name: Terraform Plan
      id: plan
      run: |
        cd terraform/environments/prod
        # Set sensitive variables as environment variables
        export TF_VAR_db_password="${{ secrets.DB_PASSWORD }}"
        export TF_VAR_jwt_secret="${{ secrets.JWT_SECRET }}"

        # Ensure the credentials file is properly set
        echo "Using credentials from $GOOGLE_APPLICATION_CREDENTIALS"

        # Run plan and save output
        terraform plan -no-color -out=tfplan
        terraform show -no-color tfplan > tfplan.txt
      continue-on-error: true

    - name: Update Pull Request
      uses: actions/github-script@v6
      if: github.event_name == 'pull_request'
      env:
        PLAN: "terraform\n${{ steps.plan.outputs.stdout }}"
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
        script: |
          const fs = require('fs');
          const plan = fs.readFileSync('terraform/environments/prod/tfplan.txt', 'utf8');
          const maxGitHubBodyLength = 65536;
          const planSummary = plan.length > maxGitHubBodyLength
            ? plan.substring(0, maxGitHubBodyLength - 8) + '...\n\n[Plan too long to display completely]'
            : plan;

          const output = `#### Terraform Initialization ⚙️\`${{ steps.init.outcome }}\`
          #### Terraform Validation 🤖\`${{ steps.validate.outcome }}\`
          #### Terraform Plan 📖\`${{ steps.plan.outcome }}\`

          <details><summary>Show Plan</summary>

          \`\`\`terraform
          ${planSummary}
          \`\`\`

          </details>

          *Pushed by: @${{ github.actor }}, Action: \`${{ github.event_name }}\`*`;

          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: output
          })


    - name: Delete Existing Cloud Run Services
      if: github.ref == 'refs/heads/main' && github.event_name == 'push'
      run: |
        # Explicitly delete the Cloud Run services to ensure clean deployment
        echo "Deleting existing Cloud Run services..."
        gcloud run services delete ielts-client --region=${{ env.REGION }} --quiet || echo "Service ielts-client does not exist or could not be deleted"
        gcloud run services delete ielts-server --region=${{ env.REGION }} --quiet || echo "Service ielts-server does not exist or could not be deleted"
        
        # Add a short delay to ensure deletions are processed
        echo "Waiting for deletions to process..."
        sleep 15

    - name: Terraform Apply
      if: github.ref == 'refs/heads/main' && github.event_name == 'push'
      run: |
        cd terraform/environments/prod
        # Set sensitive variables as environment variables
        export TF_VAR_db_password="${{ secrets.DB_PASSWORD }}"
        export TF_VAR_jwt_secret="${{ secrets.JWT_SECRET }}"

        # Ensure the credentials file is properly set
        echo "Using credentials from $GOOGLE_APPLICATION_CREDENTIALS"

        # Run apply
        terraform apply -auto-approve

    - name: Output Infrastructure URLs
      if: github.ref == 'refs/heads/main' && github.event_name == 'push'
      run: |
        cd terraform/environments/prod
        echo "::notice::Backend URL: $(terraform output -raw backend_url)"
        echo "::notice::Frontend URL: $(terraform output -raw frontend_url)"
        echo "::notice::Monitoring Dashboard URL: $(terraform output -raw monitoring_dashboard_url)"
        
    - name: Run Database Migrations
      if: github.ref == 'refs/heads/main' && github.event_name == 'push'
      run: |
        # Use migration job name from configuration
        MIGRATION_JOB="db-migrate"
        
        echo "Running database migrations using job: $MIGRATION_JOB"
        gcloud run jobs execute $MIGRATION_JOB --region=${{ env.REGION }} --wait
        
        echo "Database migrations completed successfully"

  # Deploy images to Cloud Run
  deploy-images:
    name: Deploy Images to Cloud Run
    needs: [deploy-infra]
    if: ${{ github.event.inputs.deploy_infra_only != 'true' && (github.ref == 'refs/heads/main' && github.event_name == 'push' || github.event.inputs.deploy_images_only == 'true') }}
    runs-on: ubuntu-latest

    steps:
    - name: Checkout
      uses: actions/checkout@v3
      with:
        submodules: recursive
        token: ${{ secrets.PAT_TOKEN }}

    - name: Authenticate to Google Cloud
      run: |
        # Write the service account key to a file
        echo '${{ secrets.GCP_SA_KEY }}' > sa-key.json

        # Authenticate with the service account key
        gcloud auth activate-service-account --key-file=sa-key.json

        # Set the application default credentials
        export GOOGLE_APPLICATION_CREDENTIALS="$(pwd)/sa-key.json"
        echo "GOOGLE_APPLICATION_CREDENTIALS=$(pwd)/sa-key.json" >> $GITHUB_ENV

    - name: Set up Google Cloud SDK
      uses: google-github-actions/setup-gcloud@v1
      with:
        project_id: ${{ env.PROJECT_ID }}

    - name: Deploy Images to Cloud Run
      run: |
        # Deploy backend image to Cloud Run
        gcloud run services update ielts-server \
          --image ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/ielts-server:latest \
          --region ${{ env.REGION }} \
          --platform managed

        # Deploy frontend image to Cloud Run
        gcloud run services update ielts-client \
          --image ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/ielts-client:latest \
          --region ${{ env.REGION }} \
          --platform managed

    
