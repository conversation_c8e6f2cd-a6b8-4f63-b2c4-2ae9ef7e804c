# Automated Deployment to Google Cloud Run

This document outlines the automated deployment process for the IELTS Toolkit application to Google Cloud Run.

## Prerequisites

- Google Cloud Platform account
- Google Cloud SDK installed locally
- Terraform installed locally
- Docker installed locally
- Git repository with the application code
- Git submodules properly initialized (the server-nest code is in a submodule)

## Deployment Architecture

The deployment architecture consists of:

1. **Frontend**: React application deployed to Cloud Run
2. **Backend**: NestJS application deployed to Cloud Run
3. **Database**: PostgreSQL database on Cloud SQL
4. **Infrastructure**: Managed with Terraform
5. **CI/CD**: Automated with Cloud Build or GitHub Actions

## Automated Deployment Options

You have two options for automated deployment:

### Option 1: Cloud Build (Recommended)

1. **Initial Setup**:
   ```bash
   # Make scripts executable
   chmod +x scripts/*.sh

   # Run setup script (you can use either format: --project-id=VALUE or --project-id VALUE)
   ./scripts/setup.sh --project-id=YOUR_GCP_PROJECT_ID

   # Set up Cloud Build trigger
   ./scripts/setup-cloud-build-trigger.sh --project-id=YOUR_GCP_PROJECT_ID --repo-owner=YOUR_GITHUB_USERNAME --repo-name=YOUR_REPO_NAME
   ```

   **Important**: When setting up the Cloud Build trigger in the Google Cloud Console, make sure to enable the "Include submodules" option under the advanced settings.

2. **Continuous Deployment**:
   - Push changes to the main branch
   - Cloud Build will automatically build and deploy the application

### Option 2: GitHub Actions

1. **Initial Setup**:
   - Create the following secrets in your GitHub repository:
     - `GCP_PROJECT_ID`: Your GCP project ID
     - `GCP_SA_KEY`: Service account key with necessary permissions
     - `DB_PASSWORD`: Database password
     - `JWT_SECRET`: JWT secret key

2. **Continuous Deployment**:
   - Push changes to the main branch
   - GitHub Actions will automatically build and deploy the application

### Option 3: Manual Deployment

If you prefer to deploy manually:

```bash
# Make scripts executable
chmod +x scripts/*.sh

# Clone the repository with submodules if you haven't already
git clone --recurse-submodules YOUR_REPOSITORY_URL
cd YOUR_REPOSITORY

# Run deployment script
./scripts/deploy.sh --project-id=YOUR_GCP_PROJECT_ID --manual
```

## Infrastructure as Code

The infrastructure is managed with Terraform:

- `terraform/main.tf`: Main Terraform configuration
- `terraform/variables.tf`: Variable definitions
- `terraform/terraform.tfvars`: Variable values (created by setup script)

## CI/CD Pipeline

The CI/CD pipeline consists of the following steps:

1. **Build**: Build Docker images for frontend and backend
2. **Test**: Run tests (if configured)
3. **Deploy Infrastructure**: Apply Terraform configuration
4. **Deploy Applications**: Deploy to Cloud Run
5. **Run Migrations**: Run database migrations
6. **Verify**: Output deployment URLs

## Customization

You can customize the deployment by:

1. Modifying Terraform variables in `terraform/terraform.tfvars`
2. Updating Cloud Build configuration in `cloudbuild.yaml`
3. Modifying GitHub Actions workflow in `.github/workflows/deploy.yml`

## Troubleshooting

If you encounter issues:

1. Check Cloud Build logs
2. Verify Terraform state
3. Check Cloud Run service logs
4. Ensure database connectivity

## Cleanup

To clean up all resources:

```bash
cd terraform
terraform destroy
```
