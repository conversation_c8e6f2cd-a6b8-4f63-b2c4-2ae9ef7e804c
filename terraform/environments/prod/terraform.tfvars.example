# General configuration
project_id         = "your-gcp-project-id"
region             = "us-central1"
zone               = "us-central1-a"

# Networking configuration
network_name       = "ielts-toolkit-network"
subnet_name        = "ielts-toolkit-subnet"
subnet_cidr        = "10.0.0.0/24"
connector_name     = "ielts-toolkit-connector"
connector_cidr     = "10.8.0.0/28"

# Database configuration
db_instance_name   = "ielts-toolkit-db"
db_version         = "POSTGRES_14"
db_tier            = "db-g1-small"
db_name            = "ielts_toolkit"
db_user            = "ielts_admin"
db_password        = "REPLACE_WITH_SECURE_PASSWORD" # Should be set via environment variable

# Registry configuration
repository_id      = "ielts-toolkit"
repository_format  = "DOCKER"

# Secret configuration
jwt_secret         = "REPLACE_WITH_SECURE_JWT_SECRET" # Should be set via environment variable

# Cloud Run configuration
backend_name       = "ielts-server"
frontend_name      = "ielts-client"
migration_job_name = "db-migrate"
backend_cpu        = "1000m"
backend_memory     = "512Mi"
frontend_cpu       = "1000m"
frontend_memory    = "512Mi"
backend_min_scale  = 1
backend_max_scale  = 10
frontend_min_scale = 1
frontend_max_scale = 10

# Monitoring configuration
notification_email = "<EMAIL>" # Should be set via environment variable
cpu_utilization_threshold    = 80
memory_utilization_threshold = 80
error_rate_threshold         = 5
latency_threshold            = 500
