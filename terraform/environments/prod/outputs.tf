output "network_id" {
  description = "ID of the VPC network"
  value       = module.networking.vpc_network_id
}

output "subnet_id" {
  description = "ID of the subnet"
  value       = module.networking.subnet_id
}

output "connector_id" {
  description = "ID of the VPC Access Connector"
  value       = module.networking.vpc_connector_id
}

output "db_instance_name" {
  description = "Name of the Cloud SQL instance"
  value       = module.database.instance_name
}

output "db_connection_name" {
  description = "Connection name of the Cloud SQL instance"
  value       = "${var.project_id}:${var.region}:${module.database.instance_name}"
}

output "db_private_ip" {
  description = "Private IP address of the Cloud SQL instance"
  value       = module.database.private_ip
}

output "repository_url" {
  description = "URL of the Artifact Registry repository"
  value       = "${module.registry.repository_location}-docker.pkg.dev/${var.project_id}/${module.registry.repository_id}"
}

output "backend_url" {
  description = "URL of the backend Cloud Run service"
  value       = module.cloudrun.backend_url
}

output "frontend_url" {
  description = "URL of the frontend Cloud Run service"
  value       = module.cloudrun.frontend_url
}

output "migration_job_name" {
  description = "Name of the database migration job"
  value       = module.cloudrun.migration_job_name
}

output "monitoring_dashboard_url" {
  description = "URL to the monitoring dashboard"
  value       = module.monitoring.dashboard_url
}

output "notification_channel_id" {
  description = "ID of the notification channel"
  value       = module.monitoring.notification_channel_id
}
