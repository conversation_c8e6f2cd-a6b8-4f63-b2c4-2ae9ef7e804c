variable "project_id" {
  description = "The Google Cloud project ID"
  type        = string
}

variable "region" {
  description = "The Google Cloud region"
  type        = string
  default     = "us-central1"
}

variable "zone" {
  description = "The Google Cloud zone"
  type        = string
  default     = "us-central1-a"
}

# API variables
variable "apis" {
  description = "List of APIs to enable"
  type        = list(string)
  default = [
    "cloudresourcemanager.googleapis.com",
    "compute.googleapis.com",
    "containerregistry.googleapis.com",
    "artifactregistry.googleapis.com",
    "run.googleapis.com",
    "vpcaccess.googleapis.com",
    "sqladmin.googleapis.com",
    "secretmanager.googleapis.com",
    "monitoring.googleapis.com",
    "logging.googleapis.com",
    "cloudbuild.googleapis.com"
  ]
}

# Networking variables
variable "network_name" {
  description = "Name of the VPC network"
  type        = string
  default     = "ielts-toolkit-network"
}

variable "subnet_name" {
  description = "Name of the subnet"
  type        = string
  default     = "ielts-toolkit-subnet"
}

variable "subnet_cidr" {
  description = "CIDR range for the subnet"
  type        = string
  default     = "10.0.0.0/24"
}

variable "connector_name" {
  description = "Name of the VPC Access Connector"
  type        = string
  default     = "ielts-toolkit-connector"
}

variable "connector_cidr" {
  description = "CIDR range for the VPC Access Connector"
  type        = string
  default     = "********/28"
}

# Database variables
variable "db_instance_name" {
  description = "Name of the Cloud SQL instance"
  type        = string
  default     = "ielts-toolkit-db"
}

variable "db_version" {
  description = "PostgreSQL version"
  type        = string
  default     = "POSTGRES_14"
}

variable "db_tier" {
  description = "Machine type for the database instance"
  type        = string
  default     = "db-g1-small"
}

variable "db_name" {
  description = "Name of the database"
  type        = string
  default     = "ielts_toolkit"
}

variable "db_user" {
  description = "Database user"
  type        = string
  default     = "ielts_admin"
}

variable "db_password" {
  description = "Database password"
  type        = string
  sensitive   = true
}

# Registry variables
variable "repository_id" {
  description = "ID of the Artifact Registry repository"
  type        = string
  default     = "ielts-toolkit"
}

variable "repository_format" {
  description = "Format of the Artifact Registry repository"
  type        = string
  default     = "DOCKER"
}

# Secret variables
variable "jwt_secret" {
  description = "JWT secret for authentication"
  type        = string
  sensitive   = true
}

# Cloud Run variables
variable "backend_name" {
  description = "Name of the backend Cloud Run service"
  type        = string
  default     = "ielts-server"
}

variable "frontend_name" {
  description = "Name of the frontend Cloud Run service"
  type        = string
  default     = "ielts-client"
}

variable "migration_job_name" {
  description = "Name of the database migration job"
  type        = string
  default     = "db-migrate"
}

variable "backend_cpu" {
  description = "CPU allocation for the backend service"
  type        = string
  default     = "1000m"
}

variable "backend_memory" {
  description = "Memory allocation for the backend service"
  type        = string
  default     = "512Mi"
}

variable "frontend_cpu" {
  description = "CPU allocation for the frontend service"
  type        = string
  default     = "1000m"
}

variable "frontend_memory" {
  description = "Memory allocation for the frontend service"
  type        = string
  default     = "512Mi"
}

variable "backend_min_scale" {
  description = "Minimum number of instances for the backend service"
  type        = number
  default     = 1
}

variable "backend_max_scale" {
  description = "Maximum number of instances for the backend service"
  type        = number
  default     = 10
}

variable "frontend_min_scale" {
  description = "Minimum number of instances for the frontend service"
  type        = number
  default     = 1
}

variable "frontend_max_scale" {
  description = "Maximum number of instances for the frontend service"
  type        = number
  default     = 10
}

# Monitoring variables
variable "notification_email" {
  description = "Email address for monitoring alerts"
  type        = string
}

variable "cpu_utilization_threshold" {
  description = "CPU utilization threshold for alerts (percentage)"
  type        = number
  default     = 80
}

variable "memory_utilization_threshold" {
  description = "Memory utilization threshold for alerts (percentage)"
  type        = number
  default     = 80
}

variable "error_rate_threshold" {
  description = "Error rate threshold for alerts (percentage)"
  type        = number
  default     = 5
}

variable "latency_threshold" {
  description = "Latency threshold for alerts (ms)"
  type        = number
  default     = 500
}
