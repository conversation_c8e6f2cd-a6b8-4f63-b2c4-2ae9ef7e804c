provider "google" {
  project = var.project_id
  region  = var.region
  zone    = var.zone
}

# Enable required APIs
module "apis" {
  source     = "../../modules/apis"
  project_id = var.project_id
  apis       = var.apis
}

# Create networking resources
module "networking" {
  source         = "../../modules/networking"
  project_id     = var.project_id
  region         = var.region
  name           = var.network_name
  subnet_cidr    = var.subnet_cidr
  connector_cidr = var.connector_cidr
  connector_name = var.connector_name

  depends_on = [module.apis]
}

# Create Artifact Registry repository
module "registry" {
  source        = "../../modules/registry"
  project_id    = var.project_id
  region        = var.region
  repository_id = var.repository_id
  format        = var.repository_format

  depends_on = [module.apis]
}

# Create database
module "database" {
  source         = "../../modules/database"
  project_id     = var.project_id
  region         = var.region
  vpc_network_id = module.networking.vpc_network_id
  instance_name  = var.db_instance_name
  db_tier        = var.db_tier
  db_name        = var.db_name
  db_user        = var.db_user
  db_password    = var.db_password

  depends_on = [module.networking]
}

# Create secrets
module "secrets" {
  source               = "../../modules/secrets"
  project_id           = var.project_id
  region               = var.region
  db_connection_string = "postgresql://${var.db_user}:${var.db_password}@${module.database.private_ip}:5432/${var.db_name}"
  jwt_secret_value     = var.jwt_secret

  depends_on = [module.apis, module.database]
}

# Create Cloud Run services
module "cloudrun" {
  source        = "../../modules/cloudrun"
  project_id    = var.project_id
  region        = var.region
  repository_id = var.repository_id

  backend_name       = var.backend_name
  frontend_name      = var.frontend_name
  migration_job_name = var.migration_job_name

  backend_cpu     = var.backend_cpu
  backend_memory  = var.backend_memory
  frontend_cpu    = var.frontend_cpu
  frontend_memory = var.frontend_memory

  backend_min_scale  = tostring(var.backend_min_scale)
  backend_max_scale  = tostring(var.backend_max_scale)
  frontend_min_scale = tostring(var.frontend_min_scale)
  frontend_max_scale = tostring(var.frontend_max_scale)

  vpc_connector_name = var.connector_name
  vpc_connector_id   = module.networking.vpc_connector_id

  database_url_secret_id = module.secrets.database_url_secret_id
  jwt_secret_id          = module.secrets.jwt_secret_id
  db_connection_string   = module.database.connection_string

  labels = {
    "app" = "ielts-toolkit"
    "env" = "prod"
  }

  depends_on = [
    module.apis,
    module.networking,
    module.registry,
    module.database,
    module.secrets
  ]
}

# Create monitoring resources
module "monitoring" {
  source     = "../../modules/monitoring"
  project_id = var.project_id
  region     = var.region

  backend_service_id  = module.cloudrun.backend_service_id
  frontend_service_id = module.cloudrun.frontend_service_id
  backend_name        = var.backend_name
  frontend_name       = var.frontend_name

  notification_email           = var.notification_email
  cpu_utilization_threshold    = var.cpu_utilization_threshold
  memory_utilization_threshold = var.memory_utilization_threshold
  error_rate_threshold         = var.error_rate_threshold
  latency_threshold            = var.latency_threshold

  depends_on = [module.cloudrun]
}
