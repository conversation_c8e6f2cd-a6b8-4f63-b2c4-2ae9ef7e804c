# Check if VPC Network exists
data "google_compute_network" "network" {
  name    = var.name
  project = var.project_id
}

# VPC Network - only create if it doesn't exist
resource "google_compute_network" "vpc_network" {
  count                   = 0
  name                    = var.name
  project                 = var.project_id
  auto_create_subnetworks = false
  
  lifecycle {
    prevent_destroy = true
  }
}

# Check if Subnet exists
data "google_compute_subnetwork" "subnet" {
  name    = "${var.name}-subnet"
  project = var.project_id
  region  = var.region
}

# Subnet - only create if it doesn't exist
resource "google_compute_subnetwork" "subnet" {
  count         = 0
  name          = "${var.name}-subnet"
  project       = var.project_id
  region        = var.region
  network       = data.google_compute_network.network.id
  ip_cidr_range = var.subnet_cidr

  private_ip_google_access = true
  
  lifecycle {
    prevent_destroy = true
  }
}

# Local variables for configuration
locals {
  # Network and connector configuration
  network_name = var.name
  connector_name = "ielts-toolkit-connector"
}

# Skip creating the VPC Access Connector since it already exists
resource "google_vpc_access_connector" "connector" {
  count         = 0
  name          = local.connector_name
  project       = var.project_id
  region        = var.region
  network       = var.name
  ip_cidr_range = var.connector_cidr

  # Use a smaller machine type to reduce CPU quota requirements
  machine_type = "e2-micro"

  # VPC connector requires at least 2 minimum instances
  # Max instances must be greater than min instances
  min_instances = 2
  max_instances = 3
}

# Check if Private IP allocation exists
data "google_compute_global_address" "private_ip" {
  name    = "${var.name}-private-ip"
  project = var.project_id
}

# Private IP allocation for Cloud SQL - only create if it doesn't exist
resource "google_compute_global_address" "private_ip_address" {
  count         = 0
  name          = "${var.name}-private-ip"
  project       = var.project_id
  purpose       = "VPC_PEERING"
  address_type  = "INTERNAL"
  prefix_length = 16
  network       = data.google_compute_network.network.id
  
  lifecycle {
    prevent_destroy = true
  }
}

# Private service access for Cloud SQL - only create if needed
resource "google_service_networking_connection" "private_vpc_connection" {
  count                   = 0
  network                 = data.google_compute_network.network.id
  service                 = "servicenetworking.googleapis.com"
  reserved_peering_ranges = [data.google_compute_global_address.private_ip.name]
  
  lifecycle {
    prevent_destroy = true
  }
}
