output "vpc_network_id" {
  description = "The ID of the VPC network"
  value       = data.google_compute_network.network.id
}

output "vpc_network_name" {
  description = "The name of the VPC network"
  value       = data.google_compute_network.network.name
}

output "subnet_id" {
  description = "The ID of the subnet"
  value       = data.google_compute_subnetwork.subnet.id
}

output "vpc_connector_id" {
  description = "The ID of the VPC Access Connector"
  value       = "projects/${var.project_id}/locations/${var.region}/connectors/${local.connector_name}"
}

output "vpc_connector_name" {
  description = "The name of the VPC Access Connector"
  value       = local.connector_name
}

output "private_ip_address" {
  description = "The private IP address for Cloud SQL"
  value       = data.google_compute_global_address.private_ip.address
}
