variable "project_id" {
  description = "The Google Cloud project ID"
  type        = string
}

variable "region" {
  description = "The Google Cloud region"
  type        = string
}

variable "name" {
  description = "Base name for networking resources"
  type        = string
  default     = "ielts-vpc"
}

variable "subnet_cidr" {
  description = "CIDR range for the subnet"
  type        = string
  default     = "10.0.0.0/24"
}

variable "connector_cidr" {
  description = "CIDR range for the VPC Access Connector"
  type        = string
  default     = "********/28"
}

variable "connector_name" {
  description = "Name for the VPC Access Connector"
  type        = string
  default     = "ielts-toolkit-connector"
}
