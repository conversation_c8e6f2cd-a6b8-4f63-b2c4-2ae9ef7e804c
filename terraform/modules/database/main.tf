# Local for database configuration
locals {
  instance_name = var.instance_name
}

# Note: Cloud SQL instance is managed outside of Terraform
# We use a data source to reference the existing instance

# Data source for existing Cloud SQL instance
data "google_sql_database_instance" "existing" {
  name    = local.instance_name
  project = var.project_id
}

# Local variables for database configuration
locals {
  db_name = var.db_name
  db_user = var.db_user
}

# Note: Database and user are managed outside of Terraform
# We use local variables to reference the existing database and user
