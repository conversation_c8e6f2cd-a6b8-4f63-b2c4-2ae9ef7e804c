output "instance_name" {
  description = "The name of the database instance"
  value       = data.google_sql_database_instance.existing.name
}

output "database_name" {
  description = "The name of the database"
  value       = local.db_name
}

output "connection_string" {
  description = "The connection string for the database"
  value       = "postgresql://${local.db_user}:${var.db_password}@${data.google_sql_database_instance.existing.private_ip_address}:5432/${local.db_name}"
  sensitive   = true
}

output "private_ip" {
  description = "The private IP address of the database instance"
  value       = data.google_sql_database_instance.existing.private_ip_address
}
