# Get project information
data "google_project" "project" {
  project_id = var.project_id
}

# Local for secret configuration
locals {
  database_url_secret_id = "DATABASE_URL"
  jwt_secret_id = "JWT_SECRET"
}

# Note: Secret Manager secrets are managed outside of Terraform
# We use data sources to reference the existing secrets

# Database URL secret - use data source to reference existing secret
data "google_secret_manager_secret" "database_url" {
  project   = var.project_id
  secret_id = local.database_url_secret_id
}

# Update the secret version with the latest connection string
resource "google_secret_manager_secret_version" "database_url_version" {
  secret      = data.google_secret_manager_secret.database_url.id
  secret_data = var.db_connection_string
}

# JWT secret - use data source to reference existing secret
data "google_secret_manager_secret" "jwt_secret" {
  project   = var.project_id
  secret_id = local.jwt_secret_id
}

# Update the JWT secret version with the latest value
resource "google_secret_manager_secret_version" "jwt_secret_version" {
  secret      = data.google_secret_manager_secret.jwt_secret.id
  secret_data = var.jwt_secret_value
}

# Note: IAM bindings for Cloud Run service account to access secrets are managed outside of Terraform
# This is because the Cloud Run service account is created by Google Cloud when the Cloud Run service is created
# and we need to ensure the service exists before we can grant it access to secrets.
