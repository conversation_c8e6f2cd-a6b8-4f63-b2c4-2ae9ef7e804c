variable "project_id" {
  description = "The Google Cloud project ID"
  type        = string
}

variable "region" {
  description = "The Google Cloud region"
  type        = string
}

variable "repository_id" {
  description = "ID of the Artifact Registry repository"
  type        = string
}

variable "backend_name" {
  description = "Name of the backend Cloud Run service"
  type        = string
  default     = "ielts-server"
}

variable "frontend_name" {
  description = "Name of the frontend Cloud Run service"
  type        = string
  default     = "ielts-client"
}

variable "migration_job_name" {
  description = "Name of the database migration job"
  type        = string
  default     = "db-migrate"
}

variable "backend_cpu" {
  description = "CPU allocation for the backend service"
  type        = string
  default     = "1000m"
}

variable "backend_memory" {
  description = "Memory allocation for the backend service"
  type        = string
  default     = "512Mi"
}

variable "frontend_cpu" {
  description = "CPU allocation for the frontend service"
  type        = string
  default     = "1000m"
}

variable "frontend_memory" {
  description = "Memory allocation for the frontend service"
  type        = string
  default     = "512Mi"
}

variable "backend_min_scale" {
  description = "Minimum number of instances for the backend service"
  type        = string
  default     = "1"
}

variable "backend_max_scale" {
  description = "Maximum number of instances for the backend service"
  type        = string
  default     = "10"
}

variable "frontend_min_scale" {
  description = "Minimum number of instances for the frontend service"
  type        = string
  default     = "1"
}

variable "frontend_max_scale" {
  description = "Maximum number of instances for the frontend service"
  type        = string
  default     = "10"
}

variable "vpc_connector_name" {
  description = "Name of the VPC connector"
  type        = string
}



variable "vpc_connector_id" {
  description = "ID of the VPC Access Connector"
  type        = string
}

variable "database_url_secret_id" {
  description = "Secret ID for the database connection string"
  type        = string
}

variable "jwt_secret_id" {
  description = "Secret ID for the JWT secret"
  type        = string
}

variable "db_connection_string" {
  description = "Database connection string"
  type        = string
  sensitive   = true
}

variable "labels" {
  description = "Labels to apply to the Cloud Run services"
  type        = map(string)
  default     = {}
}
