# Random suffix for the frontend service URL
resource "random_id" "suffix" {
  byte_length = 4
}

# Local variables for service references
locals {
  backend_name = var.backend_name
  frontend_name = var.frontend_name
  # Standard Cloud Run URL pattern
  backend_url = "https://${var.backend_name}-${random_id.suffix.hex}.a.run.app"
  frontend_url = "https://${var.frontend_name}-${random_id.suffix.hex}.a.run.app"
  region = var.region
  project_id = var.project_id
}

# IAM policy for public access to Cloud Run services
data "google_iam_policy" "noauth" {
  binding {
    role = "roles/run.invoker"
    members = [
      "allUsers",
    ]
  }
}

# Check if backend service already exists
data "google_cloud_run_service" "existing_backend" {
  count    = 0 # Set to 1 to check if exists or 0 to ignore
  name     = var.backend_name
  location = var.region
  project  = var.project_id
}

# Add Cloud SQL Client role to the Cloud Run service account for database access
resource "google_project_iam_member" "cloud_sql_client" {
  project = var.project_id
  role    = "roles/cloudsql.client"
  member  = "serviceAccount:cloudrun-serviceaccount@${var.project_id}.iam.gserviceaccount.com"
}

# Backend Cloud Run service
resource "google_cloud_run_service" "backend" {
  name     = var.backend_name
  location = var.region
  project  = var.project_id



  template {
    spec {
      # Use a dedicated service account for Cloud Run
      service_account_name = "cloudrun-serviceaccount@${var.project_id}.iam.gserviceaccount.com"

      containers {
        image = "${var.region}-docker.pkg.dev/${var.project_id}/${var.repository_id}/ielts-server:latest"

        # Resource limits
        resources {
          limits = {
            cpu    = var.backend_cpu
            memory = var.backend_memory
          }
        }

        # Environment variables
        env {
          name  = "NODE_ENV"
          value = "production"
        }

        env {
          name  = "CORS_ORIGIN"
          value = local.frontend_url
        }

        env {
          name  = "FRONTEND_URL"
          value = local.frontend_url
        }

        # Secret environment variables
        env {
          name = "DATABASE_URL"
          value_from {
            secret_key_ref {
              name = var.database_url_secret_id
              key  = "latest"
            }
          }
        }

        env {
          name = "JWT_SECRET"
          value_from {
            secret_key_ref {
              name = var.jwt_secret_id
              key  = "latest"
            }
          }
        }
      }
    }

    metadata {
      annotations = {
        "autoscaling.knative.dev/minScale"      = var.backend_min_scale
        "autoscaling.knative.dev/maxScale"      = var.backend_max_scale
        "run.googleapis.com/vpc-access-connector" = var.vpc_connector_name
        "run.googleapis.com/vpc-access-egress"    = "private-ranges-only"
        # Increase startup probe timeout to give the container more time to start and connect to the database
        "run.googleapis.com/startup-probe-timeout-seconds" = "600"
        # Configure container port explicitly
        "run.googleapis.com/container-port" = "8080"
        # Increase the request timeout
        "run.googleapis.com/timeoutSeconds" = "300"
      }
      labels = var.labels
    }
  }

  # Traffic configuration
  traffic {
    percent         = 100
    latest_revision = true
  }

  # Avoid unnecessary updates and improve deployment stability
  lifecycle {
    ignore_changes = [
      # Ignore client-specific annotations that change with each deployment
      metadata.0.annotations["client.knative.dev/user-image"],
      metadata.0.annotations["run.googleapis.com/client-name"],
      metadata.0.annotations["run.googleapis.com/client-version"],
      # Ignore image changes to allow CI/CD to update the image without Terraform conflicts
      template[0].spec[0].containers[0].image,
      # Ignore template annotations that change with each deployment
      template[0].metadata[0].annotations["client.knative.dev/user-image"],
      template[0].metadata[0].annotations["run.googleapis.com/client-name"],
      template[0].metadata[0].annotations["run.googleapis.com/client-version"]
    ]
    # Create new resources before destroying old ones to minimize downtime
    create_before_destroy = true
  }
}

# Check if frontend service already exists
data "google_cloud_run_service" "existing_frontend" {
  count    = 0 # Set to 1 to check if exists or 0 to ignore
  name     = var.frontend_name
  location = var.region
  project  = var.project_id
}

# Frontend Cloud Run service
resource "google_cloud_run_service" "frontend" {
  name     = var.frontend_name
  location = var.region
  project  = var.project_id



  template {
    spec {
      # Use a dedicated service account for Cloud Run
      service_account_name = "cloudrun-serviceaccount@${var.project_id}.iam.gserviceaccount.com"

      containers {
        image = "${var.region}-docker.pkg.dev/${var.project_id}/${var.repository_id}/ielts-client:latest"

        # Resource limits
        resources {
          limits = {
            cpu    = var.frontend_cpu
            memory = var.frontend_memory
          }
        }

        # Environment variables
        env {
          name  = "VITE_API_URL"
          value = "${local.backend_url}/api"
        }
      }
    }

    metadata {
      annotations = {
        "autoscaling.knative.dev/minScale" = var.frontend_min_scale
        "autoscaling.knative.dev/maxScale" = var.frontend_max_scale
        # Increase startup probe timeout to give the container more time to start
        "run.googleapis.com/startup-probe-timeout-seconds" = "300"
        # Configure container port explicitly
        "run.googleapis.com/container-port" = "8080"
        # Increase the request timeout
        "run.googleapis.com/timeoutSeconds" = "300"
        # Use CPU always allocated for better startup performance
        "run.googleapis.com/cpu-throttling" = "false"
      }
      labels = var.labels
    }
  }

  # Traffic configuration
  traffic {
    percent         = 100
    latest_revision = true
  }

  # Avoid unnecessary updates and improve deployment stability
  lifecycle {
    ignore_changes = [
      # Ignore client-specific annotations that change with each deployment
      metadata.0.annotations["client.knative.dev/user-image"],
      metadata.0.annotations["run.googleapis.com/client-name"],
      metadata.0.annotations["run.googleapis.com/client-version"],
      # Ignore image changes to allow CI/CD to update the image without Terraform conflicts
      template[0].spec[0].containers[0].image,
      # Ignore template annotations that change with each deployment
      template[0].metadata[0].annotations["client.knative.dev/user-image"],
      template[0].metadata[0].annotations["run.googleapis.com/client-name"],
      template[0].metadata[0].annotations["run.googleapis.com/client-version"]
    ]
    # Create new resources before destroying old ones to minimize downtime
    create_before_destroy = true
  }

  depends_on = [
    google_cloud_run_service.backend
  ]
}

# Set IAM policy for backend service (public access)
resource "google_cloud_run_service_iam_policy" "backend_noauth" {
  location    = google_cloud_run_service.backend.location
  project     = google_cloud_run_service.backend.project
  service     = google_cloud_run_service.backend.name
  policy_data = data.google_iam_policy.noauth.policy_data
}

# Set IAM policy for frontend service (public access)
resource "google_cloud_run_service_iam_policy" "frontend_noauth" {
  location    = google_cloud_run_service.frontend.location
  project     = google_cloud_run_service.frontend.project
  service     = google_cloud_run_service.frontend.name
  policy_data = data.google_iam_policy.noauth.policy_data
}

# Use a data source to reference the existing db_migrate job instead of managing it as a resource
data "google_cloud_run_v2_job" "db_migrate" {
  name     = var.migration_job_name
  location = var.region
  project  = var.project_id
}
