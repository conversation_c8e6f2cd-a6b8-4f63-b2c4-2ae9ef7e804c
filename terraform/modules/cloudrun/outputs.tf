output "backend_url" {
  description = "The URL of the backend Cloud Run service"
  value       = google_cloud_run_service.backend.status[0].url
}

output "frontend_url" {
  description = "The URL of the frontend Cloud Run service"
  value       = google_cloud_run_service.frontend.status[0].url
}

output "migration_job_name" {
  description = "The name of the database migration job"
  value       = var.migration_job_name
}

output "backend_service_id" {
  description = "The ID of the backend Cloud Run service"
  value       = "projects/${local.project_id}/locations/${local.region}/services/${local.backend_name}"
}

output "frontend_service_id" {
  description = "The ID of the frontend Cloud Run service"
  value       = google_cloud_run_service.frontend.id
}
