# Local to determine repository configuration
locals {
  repository_name = var.repository_id
}

# Note: Artifact Registry repository is managed outside of Terraform
# We use a local variable to reference the existing repository

# Data source to get the existing repository
data "google_artifact_registry_repository" "repository" {
  project       = var.project_id
  location      = var.region
  repository_id = var.repository_id
}
