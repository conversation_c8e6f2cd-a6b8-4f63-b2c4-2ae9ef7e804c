variable "project_id" {
  description = "The Google Cloud project ID"
  type        = string
}

variable "region" {
  description = "The Google Cloud region"
  type        = string
}

variable "backend_service_id" {
  description = "The ID of the backend Cloud Run service"
  type        = string
}

variable "frontend_service_id" {
  description = "The ID of the frontend Cloud Run service"
  type        = string
}

variable "backend_name" {
  description = "The name of the backend Cloud Run service"
  type        = string
  default     = "ielts-server"
}

variable "frontend_name" {
  description = "The name of the frontend Cloud Run service"
  type        = string
  default     = "ielts-client"
}

variable "notification_email" {
  description = "Email address for monitoring alerts"
  type        = string
}

variable "cpu_utilization_threshold" {
  description = "CPU utilization threshold for alerts (percentage)"
  type        = number
  default     = 80
}

variable "memory_utilization_threshold" {
  description = "Memory utilization threshold for alerts (percentage)"
  type        = number
  default     = 80
}

variable "error_rate_threshold" {
  description = "Error rate threshold for alerts (percentage)"
  type        = number
  default     = 5
}

variable "latency_threshold" {
  description = "Latency threshold for alerts (ms)"
  type        = number
  default     = 500
}
